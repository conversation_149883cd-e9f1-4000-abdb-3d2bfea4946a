// components/ui/Accordion.tsx

import React, { useState } from 'react';
import {
  View,
  Text,
  TouchableOpacity,
  LayoutAnimation,
  Platform,
  UIManager,
} from 'react-native';
import { ChevronDown } from 'lucide-react-native';
import { cn } from '@/lib/utils';

if (Platform.OS === 'android' && UIManager.setLayoutAnimationEnabledExperimental) {
  UIManager.setLayoutAnimationEnabledExperimental(true);
}

// Accordion Wrapper (for grouping)
export const Accordion = ({ children }: { children: React.ReactNode }) => {
  return <View>{children}</View>;
};

// Single Item
export const AccordionItem = ({ children, className }: { children: React.ReactNode; className?: string }) => {
  return <View className={cn('border-b border-border', className)}>{children}</View>;
};

// Trigger
export const AccordionTrigger = ({
  title,
  open,
  onPress,
  className,
}: {
  title: React.ReactNode;
  open: boolean;
  onPress: () => void;
  className?: string;
}) => {
  return (
    <TouchableOpacity
      onPress={() => {
        LayoutAnimation.configureNext(LayoutAnimation.Presets.easeInEaseOut);
        onPress();
      }}
      className={cn('flex-row justify-between items-center py-4', className)}
    >
      <Text className="font-medium text-base">{title}</Text>
      <ChevronDown
        className={cn('w-4 h-4 text-muted-foreground', open ? 'rotate-180' : '')}
      />
    </TouchableOpacity>
  );
};

// Content
export const AccordionContent = ({
  open,
  children,
  className,
}: {
  open: boolean;
  children: React.ReactNode;
  className?: string;
}) => {
  if (!open) return null;
  return <View className={cn('pb-4 pt-0', className)}>{children}</View>;
};
