import React from 'react';
import ModeSelector from '@/components/ModeSelector';
import OnboardingFlow from '@/components/OnboardingFlow';
import SignupScreen from '@/components/SignupScreen';
import { useAppLogic, type Mode } from '@/hooks/useAppLogic';
import DefaultLayout from '@/components/layouts/DefaultLayout';
import NewbornLayout from '@/components/layouts/NewbornLayout';
import SplashScreen from '@/components/SplashScreen';

const Index = () => {
  const {
    showSplash,
    currentMode,
    setCurrentMode,
    showOnboarding,
    showSignup,
    currentLanguage,
    selectedDate,
    t,
    handleOnboardingComplete,
    handleSignupComplete,
    toggleLanguage
  } = useAppLogic();

  if (showSplash) {
    return <SplashScreen />;
  }

  if (showOnboarding) {
    return (
      <OnboardingFlow 
        onComplete={handleOnboardingComplete} 
        language={currentLanguage}
        toggleLanguage={toggleLanguage}
        t={t}
      />
    );
  }

  if (showSignup) {
    return (
      <SignupScreen 
        onComplete={handleSignupComplete}
        selectedMode={currentMode}
        language={currentLanguage}
        t={t}
      />
    );
  }

  return (
    <div className="min-h-screen bg-secondary pt-4">
      <div className="max-w-4xl mx-auto p-4 space-y-6">
        <ModeSelector 
          currentMode={currentMode} 
          onModeChange={(mode: string) => setCurrentMode(mode as Mode)} 
          t={t} 
        />

        {currentMode === 'newborn' ? (
          <NewbornLayout 
            t={t}
            currentMode={currentMode}
            selectedDate={selectedDate}
            language={currentLanguage}
          />
        ) : (
          <DefaultLayout
            t={t}
            currentMode={currentMode}
            selectedDate={selectedDate}
            language={currentLanguage}
          />
        )}
      </div>
    </div>
  );
};

export default Index;
