/**
 * Below are the colors that are used in the app. The colors are defined in the light and dark mode.
 * There are many other ways to style your app. For example, [Nativewind](https://www.nativewind.dev/), [Tamagui](https://tamagui.dev/), [unistyles](https://reactnativeunistyles.vercel.app), etc.
 */

const tintColorLight = '#0a7ea4';
const tintColorDark = '#fff';

export const Colors = {
  light: {
    text: '#11181C',
    background: '#fff',
    tint: tintColorLight,
    icon: '#687076',
    tabIconDefault: '#687076',
    tabIconSelected: tintColorLight,
  },
  dark: {
    text: '#ECEDEE',
    background: '#151718',
    tint: tintColorDark,
    icon: '#9BA1A6',
    tabIconDefault: '#9BA1A6',
    tabIconSelected: tintColorDark,
  },
  background: "hsl(350, 20%, 98%)",
  foreground: "hsl(340, 15%, 15%)",
  card: "hsl(350, 25%, 97%)",
  cardForeground: "hsl(340, 15%, 15%)",

  // Primary Theme
  primary: "hsl(340, 75%, 75%)",
  primaryForeground: "hsl(0, 0%, 100%)",

  // Secondary Theme
  secondary: "hsl(345, 30%, 92%)",
  secondaryForeground: "hsl(340, 40%, 25%)",

  // Muted / Accent
  muted: "hsl(345, 20%, 94%)",
  mutedForeground: "hsl(340, 10%, 50%)",
  accent: "hsl(345, 25%, 90%)",
  accentForeground: "hsl(340, 40%, 20%)",

  // Utility
  destructive: "hsl(0, 84.2%, 60.2%)",
  destructiveForeground: "hsl(0, 0%, 98%)",
  border: "hsl(345, 15%, 88%)",
  input: "hsl(345, 15%, 88%)",
  ring: "hsl(340, 75%, 75%)",

  // Gradients (for reference)
  gradientStart: "hsl(340, 75%, 75%)",
  gradientEnd: "hsl(345, 25%, 90%)",

};
