/** @type {import('tailwindcss').Config} */
module.exports = {
  content: [
    "./App.{js,ts,jsx,tsx}",
    "./app/**/*.{js,ts,jsx,tsx}",
    "./components/**/*.{js,ts,jsx,tsx}",
  ],
  presets: [require("nativewind/preset")],
  theme: {
    extend: {
      colors: {
        background: "hsl(350, 20%, 98%)",
        foreground: "hsl(340, 15%, 15%)",

        card: "hsl(350, 25%, 97%)",
        "card-foreground": "hsl(340, 15%, 15%)",

        popover: "hsl(350, 25%, 97%)",
        "popover-foreground": "hsl(340, 15%, 15%)",

        primary: "hsl(340, 75%, 75%)",
        "primary-foreground": "hsl(0, 0%, 100%)",

        secondary: "hsl(345, 30%, 92%)",
        "secondary-foreground": "hsl(340, 40%, 25%)",

        muted: "hsl(345, 20%, 94%)",
        "muted-foreground": "hsl(340, 10%, 50%)",

        accent: "hsl(345, 25%, 90%)",
        "accent-foreground": "hsl(340, 40%, 20%)",

        destructive: "hsl(0, 84.2%, 60.2%)",
        "destructive-foreground": "hsl(0, 0%, 98%)",

        border: "hsl(345, 15%, 88%)",
        input: "hsl(345, 15%, 88%)",
        ring: "hsl(340, 75%, 75%)",
      },
      borderRadius: {
        lg: "0.75rem",
      },
      fontSize: {
        xs: 12,
        sm: 14,
        base: 16,
        lg: 20,
        xl: 24,
        title: 28,
      },
      spacing: {
        xs: 4,
        sm: 8,
        md: 16,
        lg: 24,
        xl: 32,
      },
      gradientColorStops: theme => ({
        ...theme("colors"),
        maternal: "hsl(340, 75%, 75%)",
        "maternal-accent": "hsl(345, 25%, 90%)",
      }),
    },
  },
  plugins: [],
};
