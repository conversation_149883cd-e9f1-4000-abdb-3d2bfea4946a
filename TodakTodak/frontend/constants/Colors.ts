// constants/Colors.ts

export const Colors = {
  radius: 12, // 0.75rem

  light: {
    background: '#fef9fa',          // hsl(355 25% 98%)
    foreground: '#572b2e',          // hsl(340 15% 15%)

    card: '#fdf6f7',                // hsl(355 20% 97%)
    cardForeground: '#572b2e',

    popover: '#fdf6f7',
    popoverForeground: '#572b2e',

    primary: '#e96984',             // hsl(342 65% 70%)
    primaryForeground: '#ffffff',

    secondary: '#f8e6eb',           // hsl(350 25% 92%)
    secondaryForeground: '#7d3d45', // hsl(340 35% 25%)

    muted: '#fbf2f4',               // hsl(355 20% 94%)
    mutedForeground: '#a76a7f',     // hsl(340 10% 50%)

    accent: '#f3e4e7',              // hsl(348 20% 90%)
    accentForeground: '#6b3740',    // hsl(340 35% 20%)

    destructive: '#e5484d',         // hsl(0 84.2% 60.2%)
    destructiveForeground: '#fafafa',

    border: '#eee3e6',              // hsl(350 15% 88%)
    input: '#eee3e6',
    ring: '#e96984',
  },

  dark: {
    background: '#1c1214',          // hsl(340 15% 8%)
    foreground: '#fbeef0',          // hsl(355 25% 95%)

    card: '#1a0f11',                // hsl(340 15% 10%)
    cardForeground: '#fbeef0',

    popover: '#1a0f11',
    popoverForeground: '#fbeef0',

    primary: '#e25b74',             // hsl(342 60% 65%)
    primaryForeground: '#1c1214',

    secondary: '#2d1c1f',           // hsl(340 10% 18%)
    secondaryForeground: '#fbeef0',

    muted: '#2d1c1f',
    mutedForeground: '#caa5b4',     // hsl(340 20% 65%)

    accent: '#2d1c1f',
    accentForeground: '#fbeef0',

    destructive: '#72282a',         // hsl(0 62.8% 30.6%)
    destructiveForeground: '#fafafa',

    border: '#2d1c1f',
    input: '#2d1c1f',
    ring: '#e25b74',
  },

  // Calendar dot indicators
  calendarIndicators: {
    period: 'rgba(229, 72, 77, 0.7)',         // destructive with alpha
    fertile: 'rgba(233, 105, 132, 0.5)',      // primary with alpha
    ovulation: '#e96984',                    // primary solid
    entry: 'rgba(107, 55, 64, 0.8)',          // accentForeground with alpha
  },

  // Gradients (Expo's LinearGradient colors)
  gradients: {
    maternal: ['#e96984', '#f3e4e7'],   // primary → accent
    soft: ['#f8e6eb', '#f3e4e7'],       // secondary → accent
    card: ['#f9f3f5', '#fdf6f7'],       // custom card blend
  },
}
