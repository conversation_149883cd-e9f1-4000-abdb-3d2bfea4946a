// tailwind.config.js
const { fontFamily } = require('tailwindcss/defaultTheme')

/** @type {import('tailwindcss').Config} */
module.exports = {
  darkMode: 'class',
  content: [
    "./App.{js,jsx,ts,tsx}",
    "./src/**/*.{js,jsx,ts,tsx}",
  ],
  theme: {
    extend: {
      colors: {
        background: '#fef9fa',
        foreground: '#572b2e',

        card: '#fdf6f7',
        'card-foreground': '#572b2e',

        popover: '#fdf6f7',
        'popover-foreground': '#572b2e',

        primary: '#e96984',
        'primary-foreground': '#ffffff',

        secondary: '#f8e6eb',
        'secondary-foreground': '#7d3d45',

        muted: '#fbf2f4',
        'muted-foreground': '#a76a7f',

        accent: '#f3e4e7',
        'accent-foreground': '#6b3740',

        destructive: '#e5484d',
        'destructive-foreground': '#fafafa',

        border: '#eee3e6',
        input: '#eee3e6',
        ring: '#e96984',
      },
      gradient: {
        maternalStart: '#e96984',
        maternalEnd: '#f3e4e7',
        softStart: '#f8e6eb',
        softEnd: '#f3e4e7',
        cardStart: '#f9f3f5',
        cardEnd: '#fdf6f7',
      },
      fontFamily: {
        sans: ['"Pretendard Variable"', ...fontFamily.sans],
      },
      fontSize: {
        display: ["32px", { lineHeight: "40px", letterSpacing: "-0.5px" }],
        heading: ["24px", { lineHeight: "32px", fontWeight: "600" }],
        body: ["16px", { lineHeight: "24px" }],
        small: ["14px", { lineHeight: "20px" }],
        label: ["12px", { lineHeight: "16px", fontWeight: "500" }],
      },
      spacing: {
        section: "24px", // 큰 섹션 간격
        card: "16px",    // 카드 내부 패딩
        tight: "8px",
        loose: "32px",
      },
      boxShadow: {
        card: "0px 1px 4px rgba(0,0,0,0.06)",
        header: "0px 2px 6px rgba(0,0,0,0.08)",
        modal: "0px 4px 12px rgba(0,0,0,0.12)",
      },
      elevation: {
        1: "1",
        2: "2",
        4: "4",
        8: "8",
      },
      borderRadius: {
        lg: "12px",
        xl: "16px",
        full: "9999px",
      },
    },
  },
  plugins: [],
}
